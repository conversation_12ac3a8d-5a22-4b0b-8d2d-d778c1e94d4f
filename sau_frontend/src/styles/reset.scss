/* CSS Reset - 删除浏览器默认样式 */

/* 1. Use a more-intuitive box-sizing model */
*, *::before, *::after {
  box-sizing: border-box;
}

/* 2. Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* 3. Allow percentage-based heights in the application */
html, body {
  height: 100%;
}

/* 4. Add accessible line-height and improve text rendering */
body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* 5. Improve media defaults */
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}

/* 6. Remove built-in form typography styles */
input, button, textarea, select {
  font: inherit;
}

/* 7. Avoid text overflows */
p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}

/* 8. Create a root stacking context */
#root, #__next, #app {
  isolation: isolate;
}

/* 9. Remove list styles */
ul, ol {
  list-style: none;
}

/* 10. Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* 11. Remove default link styles */
a {
  text-decoration: none;
  color: inherit;
}

/* 12. Remove default table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 13. Remove default fieldset styles */
fieldset {
  border: none;
}

/* 14. Remove default legend styles */
legend {
  display: table;
}

/* 15. Remove default details/summary styles */
details {
  display: block;
}

summary {
  display: list-item;
}

/* 16. Remove default hr styles */
hr {
  border: none;
  height: 1px;
  background: #ccc;
}

/* 17. Remove default blockquote styles */
blockquote {
  quotes: none;
}

blockquote:before,
blockquote:after {
  content: '';
  content: none;
}

/* 18. Remove default cite styles */
cite {
  font-style: normal;
}

/* 19. Remove default address styles */
address {
  font-style: normal;
}