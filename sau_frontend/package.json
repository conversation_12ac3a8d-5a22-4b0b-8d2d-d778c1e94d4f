{"name": "sau-admin", "private": true, "version": "0.0.0", "author": "<PERSON><PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "element-plus": "^2.9.11", "pinia": "^3.0.2", "sass": "^1.89.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}